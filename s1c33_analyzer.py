#!/usr/bin/env python3
"""
Epson S1C33 专用分析工具
针对 SUN.EXE 在 S1C33 架构上的深度分析
"""

import struct
import re
from collections import defaultdict

class S1C33Analyzer:
    def __init__(self, filename):
        with open(filename, 'rb') as f:
            self.data = f.read()
        self.size = len(self.data)
        
        # S1C33 指令集定义
        self.s1c33_instructions = {
            0x0000: "NOP",
            0x0001: "HALT", 
            0x0010: "RET",
            0x0020: "RETI",
            0x1000: "LD",     # Load instructions
            0x2000: "ST",     # Store instructions  
            0x3000: "ADD",    # Add instructions
            0x4000: "SUB",    # Subtract instructions
            0x5000: "AND",    # Logical AND
            0x6000: "OR",     # Logical OR
            0x7000: "XOR",    # Logical XOR
            0x8000: "CMP",    # Compare instructions
            0x9000: "JMP",    # Jump instructions
            0xA000: "CALL",   # Call instructions
            0xB000: "Bcc",    # Conditional branches
            0xC000: "BIT",    # Bit operations
            0xD000: "SHIFT",  # Shift operations
            0xE000: "MUL",    # Multiply
            0xF000: "DIV",    # Divide
        }
    
    def find_c_source_references(self):
        """查找C源文件引用和调试信息"""
        print("=== C源文件和调试信息分析 ===")
        
        # 搜索可能的文件路径
        path_patterns = [
            rb'[A-Za-z]:\\[^\\]+\\[^\\]+\.c',  # Windows路径
            rb'/[^/]+/[^/]+\.c',               # Unix路径
            rb'[a-zA-Z_][a-zA-Z0-9_]*\.c',     # 简单文件名
            rb'[a-zA-Z_][a-zA-Z0-9_]*\.h',     # 头文件
        ]
        
        found_sources = []
        for pattern in path_patterns:
            for match in re.finditer(pattern, self.data):
                pos = match.start()
                filename = match.group().decode('ascii', errors='ignore')
                found_sources.append((pos, filename))
        
        if found_sources:
            print("发现的源文件引用:")
            for pos, filename in found_sources:
                print(f"  0x{pos:04x}: {filename}")
        else:
            print("未发现明显的C源文件引用")
        
        # 搜索编译器生成的字符串
        compiler_strings = [
            b'__FILE__', b'__LINE__', b'__func__', b'__FUNCTION__',
            b'gcc', b'GCC', b'GNU', b'__GNUC__',
            b'__DATE__', b'__TIME__', b'__VERSION__'
        ]
        
        print("\n编译器特征:")
        for string in compiler_strings:
            pos = self.data.find(string)
            if pos != -1:
                print(f"  {string.decode()}: 0x{pos:04x}")
    
    def analyze_s1c33_instructions(self):
        """分析S1C33指令"""
        print("\n=== S1C33 指令分析 ===")
        
        instruction_stats = defaultdict(int)
        code_sections = []
        
        # 分析16位指令
        for i in range(0, self.size - 2, 2):
            try:
                word = struct.unpack('<H', self.data[i:i+2])[0]
                
                # 检查指令模式
                opcode_family = word & 0xF000
                if opcode_family in [op & 0xF000 for op in self.s1c33_instructions.keys()]:
                    for base_op, mnemonic in self.s1c33_instructions.items():
                        if (word & 0xF000) == (base_op & 0xF000):
                            instruction_stats[mnemonic] += 1
                            
                            # 记录可能的代码段
                            if len(code_sections) < 20:  # 限制输出
                                code_sections.append((i, word, mnemonic))
                            break
            except:
                continue
        
        print("指令统计:")
        for mnemonic, count in sorted(instruction_stats.items(), key=lambda x: x[1], reverse=True):
            if count > 10:  # 只显示频繁出现的指令
                print(f"  {mnemonic}: {count} 次")
        
        print(f"\n可能的代码段 (前20个):")
        for pos, word, mnemonic in code_sections[:20]:
            print(f"  0x{pos:04x}: 0x{word:04x} {mnemonic}")
    
    def analyze_memory_layout(self):
        """分析S1C33内存布局"""
        print("\n=== S1C33 内存布局分析 ===")
        
        # S1C33 典型内存映射
        memory_regions = {
            'ROM': (0x0000, 0x7FFF),
            'RAM': (0x8000, 0xBFFF), 
            'I/O': (0xC000, 0xFFFF),
        }
        
        # 分析地址引用
        address_refs = defaultdict(list)
        
        for i in range(0, self.size - 2, 2):
            addr = struct.unpack('<H', self.data[i:i+2])[0]
            
            for region, (start, end) in memory_regions.items():
                if start <= addr <= end:
                    address_refs[region].append((i, addr))
                    break
        
        print("内存区域引用统计:")
        for region, refs in address_refs.items():
            print(f"  {region}: {len(refs)} 个引用")
            if refs:
                # 显示前几个引用
                for pos, addr in refs[:5]:
                    print(f"    0x{pos:04x} -> 0x{addr:04x}")
                if len(refs) > 5:
                    print(f"    ... 还有 {len(refs)-5} 个")
    
    def find_interrupt_vectors(self):
        """查找中断向量表"""
        print("\n=== 中断向量表分析 ===")
        
        # S1C33 中断向量通常在ROM开始处
        print("ROM开始处的向量表:")
        for i in range(0, min(64, self.size), 2):
            vector = struct.unpack('<H', self.data[i:i+2])[0]
            print(f"  0x{i:04x}: 0x{vector:04x}")
            
            # 检查是否是有效的代码地址
            if 0x0000 <= vector <= 0x7FFF:
                print(f"    -> 可能指向ROM代码段")
    
    def analyze_string_resources(self):
        """分析字符串资源（多语言支持）"""
        print("\n=== 字符串资源分析 ===")
        
        # 已知的语言偏移
        language_offsets = {
            'default': 0x0800,
            'gb2312': 0x0810, 
            'uigur': 0x0820,
            'english': 0x0830,
            'japanese': 0x0840,
            'french': 0x0850,
        }
        
        print("多语言字符串表结构:")
        for lang, offset in language_offsets.items():
            if offset < self.size - 16:
                # 分析字符串表头
                header = self.data[offset:offset+16]
                print(f"  {lang} (0x{offset:04x}):")
                
                # 尝试解析表头结构
                try:
                    lang_id = struct.unpack('<H', header[8:10])[0]
                    str_count = struct.unpack('<H', header[10:12])[0] 
                    str_offset = struct.unpack('<I', header[12:16])[0]
                    
                    print(f"    语言ID: {lang_id}")
                    print(f"    字符串数量: {str_count}")
                    print(f"    字符串偏移: 0x{str_offset:04x}")
                except:
                    print(f"    无法解析表头")
    
    def find_function_symbols(self):
        """查找函数符号"""
        print("\n=== 函数符号分析 ===")
        
        # 常见的嵌入式函数名
        common_functions = [
            b'main', b'init', b'setup', b'loop', b'delay',
            b'lcd_init', b'lcd_clear', b'lcd_print',
            b'led_on', b'led_off', b'button_read',
            b'timer_init', b'timer_start', b'timer_stop',
            b'interrupt', b'isr', b'handler'
        ]
        
        found_functions = []
        for func_name in common_functions:
            pos = 0
            while True:
                pos = self.data.find(func_name, pos)
                if pos == -1:
                    break
                
                # 检查是否是完整的符号
                if (pos == 0 or not self.data[pos-1:pos].isalnum()) and \
                   (pos + len(func_name) >= self.size or not self.data[pos+len(func_name):pos+len(func_name)+1].isalnum()):
                    found_functions.append((pos, func_name.decode()))
                
                pos += 1
        
        if found_functions:
            print("发现的函数符号:")
            for pos, func_name in found_functions:
                print(f"  0x{pos:04x}: {func_name}")
        else:
            print("未发现明显的函数符号")
    
    def analyze_data_structures(self):
        """分析数据结构"""
        print("\n=== 数据结构分析 ===")
        
        # 查找可能的结构体数组
        print("可能的数据表:")
        
        # 分析重复的16位模式
        patterns = defaultdict(list)
        for i in range(0, self.size - 8, 2):
            pattern = self.data[i:i+8]
            patterns[pattern].append(i)
        
        # 显示重复出现的模式
        repeated_patterns = [(pattern, positions) for pattern, positions in patterns.items() 
                           if len(positions) >= 3 and len(positions) <= 20]
        
        for pattern, positions in sorted(repeated_patterns, key=lambda x: len(x[1]), reverse=True)[:10]:
            print(f"  模式 {pattern.hex()}: 出现 {len(positions)} 次")
            print(f"    位置: {[hex(p) for p in positions[:5]]}")
    
    def generate_report(self):
        """生成完整分析报告"""
        print("=" * 60)
        print("Epson S1C33 架构 - SUN.EXE 分析报告")
        print("=" * 60)
        
        self.find_c_source_references()
        self.analyze_s1c33_instructions()
        self.analyze_memory_layout()
        self.find_interrupt_vectors()
        self.analyze_string_resources()
        self.find_function_symbols()
        self.analyze_data_structures()
        
        print("\n" + "=" * 60)
        print("分析完成")
        print("=" * 60)

def main():
    try:
        analyzer = S1C33Analyzer('SUN.EXE')
        analyzer.generate_report()
    except FileNotFoundError:
        print("错误: 找不到 SUN.EXE 文件")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    main()
