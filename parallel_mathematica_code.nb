foldername = "0512";
SetDirectory[NotebookDirectory[]]; 
CreateDirectory[foldername]; 
SetDirectory[foldername]; 

(* Initialize parallel kernels *)
Needs["Parallel`"];
LaunchKernels[];
Print["Launched ", $KernelCount, " parallel kernels"];

storedata[dataList_] := 
  Module[{varName}, Table[varName = ToString[dataList[[i]]];
    Export[varName <> ".csv", ToExpression[dataList[[i]]]];, {i, 
     Length[dataList]}]];

getdata[fileList_] := 
  Module[{}, 
   Table[ToExpression[
     fileList[[i]] <> "= Import[\"" <> fileList[[i]] <> 
      ".csv\"]"], {i, Length[fileList]}]];

(* Create dummy hsmindata if not available *)
If[!FileExistsQ["hsmindata.csv"],
  Print["Creating dummy hsmindata.csv"];
  hsmindata = Table[{h, 0.0001}, {h, 0, 0.2, 0.001}];
  Export["hsmindata.csv", hsmindata];
];

getdata[{"hsmindata"}];
hsmin = Interpolation[hsmindata];(*h, Subscript[s, min] *)

getenergy[sc_?NumericQ, h_?NumericQ, w_?NumericQ, b_?NumericQ, 
   lb_?NumericQ, deltal_?NumericQ] := 
  Module[{ym, \[Theta], \[Psi], l, \[Lambda]1, \[Lambda]2, x, y, ode, 
    sol, bc, s, y0m, energy, \[Lambda]10, \[Lambda]20},
   l = lb - sc;
   If[l <= 0, Return[{10000, 10000, 10000}]];
   y0m = 4/\[Pi] (lb deltal)^(1/2);
   ym = y0m - h;
   ode = {Derivative[1][\[Theta]][s] == \[Psi][s],
     b \[Psi]'[
        s] == \[Lambda]2[s] Cos[\[Theta][s]] - \[Lambda]1[
         s] Sin[\[Theta][s]],
     x'[s] == Cos[\[Theta][s]],
     y'[s] == Sin[\[Theta][s]],
     \[Lambda]1'[s] == 0,
     \[Lambda]2'[s] == 0};
   bc = {x[0] == 0,
     y[0] == 0,
     \[Theta][0] == 0,
     x[l] == l - deltal,
     y[l] == ym,
     \[Psi][l] == 0};
   sol = 
    Quiet@Check[
      NDSolve[{ode, bc}, {\[Theta], \[Psi], x, 
        y, \[Lambda]1, \[Lambda]2}, {s, 0, l}], ($Failed)];
   If[sol =!= $Failed,
    If[(\[Psi][0] /. sol[[1]]) > 0,
     energy = 
      Quiet@Check[
        NIntegrate[(\[Psi][s] /. sol[[1]])^2, {s, 0, l}], 10000] - 
       w sc;
     \[Lambda]10 = \[Lambda]1[0] /. sol[[1]];
     \[Lambda]20 = \[Lambda]2[0] /. sol[[1]];
     sol = .;
     {energy, \[Lambda]10, \[Lambda]20}, 
     sol = .; {10000, 10000, 10000}],
    sol = .; {10000, 10000, 10000}]];

getonlyenergy[sc_?NumericQ, h_?NumericQ, w_?NumericQ, b_?NumericQ, 
   lb_?NumericQ, deltal_?NumericQ] := 
  Module[{result},
   result = getenergy[sc, h, w, b, lb, deltal];
   If[NumericQ[result[[1]]], result[[1]], 10000]];

(*Parameters - Define before distributing*)
hmax = 0.1;
hmin = 0;
vadv = 0.05;
vrec = -0.05;
t0 = 0;
tstep = 0.001;
tadv1 = hmax/vadv;
trec = (hmin - hmax)/vrec;
tadv2 = (hmax - hmin)/vadv;
tend = tadv1 + trec;
dt = tstep;
ds = 0.001;

lb = 1;
w = 1;
deltal = 1/20; 
b = 1;
\[Zeta]h = 0.01;
\[Alpha] = 0;
c = 0.05;

h[t_] := 
  Piecewise[{{vadv t, t < tadv1}, {hmax + vrec (t - tadv1), 
     trec + tadv1 >= t >= tadv1}, {hmin + vadv (t - (tadv1 + trec)), 
     tadv1 + trec + tadv2 > t > tadv1 + trec}, {hmax + 
      vrec (t - (tadv1 + trec + tadv2)), 
     tadv1 + trec + tadv2 + trec >= t >= 
      tadv1 + trec + tadv2}, {hmin + 
      vadv (t - (tadv1 + trec + tadv2 + trec)), 
     tadv1 + trec + tadv2 + trec + tadv2 > t > 
      tadv1 + trec + tadv2 + trec}, {hmax + 
      vrec (t - (tadv1 + trec + tadv2 + trec + tadv2)), 
     t >= tadv1 + trec + tadv2 + trec + tadv2}}];

(* CRITICAL: Distribute all definitions and variables to parallel kernels *)
Print["Distributing definitions to parallel kernels..."];
DistributeDefinitions[
  getenergy, getonlyenergy, h, hmax, hmin, vadv, vrec, tadv1, trec, 
  tadv2, lb, w, deltal, b, ds, \[Pi]
];

(* Verify distribution worked *)
ParallelEvaluate[Print["Kernel ", $KernelID, " received definitions"]];

getduds[t_, s0_] := 
 Module[{points, values, left4, left3, left2, left1, center, right1, 
   right2, right3, right4, der, h1},
  h1 = h[t];
  
  (* Try central difference first *)
  points = Table[s0 + i*ds, {i, {-2, -1, 1, 2}}];
  values = 
   ParallelTable[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points},
     Method -> "CoarsestGrained"];
  (* Add error checking for parallel results *)
  If[Length[values] != 4 || MemberQ[values, _Missing | _$Failed | Null],
   Print["Parallel computation failed, falling back to sequential"];
   values = Table[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points}];
   ];
  {left2, left1, right1, right2} = values;
  If[AllTrue[{left2, left1, right1, right2}, NumericQ[#] && # != 10000 &],
   der = (-right2 + 8*right1 - 8*left1 + left2)/(12*ds);
   Return[der];];
  
  (* Try forward difference *)
  points = Table[s0 + i*ds, {i, {0, 1, 2, 3, 4}}];
  values = 
   ParallelTable[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points},
     Method -> "CoarsestGrained"];
  If[Length[values] != 5 || MemberQ[values, _Missing | _$Failed | Null],
   values = Table[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points}];
   ];
  {center, right1, right2, right3, right4} = values;
  If[AllTrue[{center, right1, right2, right3, right4}, NumericQ[#] && # != 10000 &],
   der = (-25*center + 48*right1 - 36*right2 + 16*right3 - 
       3*right4)/(12*ds); Return[der];];
  
  (* Try backward difference *)
  points = Table[s0 + i*ds, {i, {-4, -3, -2, -1, 0}}];
  values = 
   ParallelTable[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points},
     Method -> "CoarsestGrained"];
  If[Length[values] != 5 || MemberQ[values, _Missing | _$Failed | Null],
   values = Table[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points}];
   ];
  {left4, left3, left2, left1, center} = values;
  If[AllTrue[{center, left4, left3, left2, left1}, NumericQ[#] && # != 10000 &],
   der = (3*left4 - 16*left3 + 36*left2 - 48*left1 + 25*center)/(12*
       ds);
   Return[der];];
   
  Return["Error_at_getduds"];]

getsnext[t_, s_] := 
  Module[{k1, k2, k3, k4, snext},
   k1 = sdot[t, s] dt;
   If[! NumericQ[k1], Return[k1]];
   k2 = sdot[t + dt/2, s + k1/2] dt;
   If[! NumericQ[k2], Return[k2]];
   k3 = sdot[t + dt/2, s + k2/2] dt;
   If[! NumericQ[k3], Return[k3]];
   k4 = sdot[t + dt, s + k3] dt;
   If[! NumericQ[k4], Return[k4]];
   snext = s + 1/6 (k1 + 2 k2 + 2 k3 + k4)];

sdot[t_, s_] := Module[{dudsResult}, 
  dudsResult = getduds[t, s];
  If[NumericQ[dudsResult], -(1/c) (dudsResult - w), dudsResult]]

(*Main simulation*)
s0 = 0.001;
ftnow = \[Zeta]h h'[t0];
undone = True;

sdata = {};
fdata = {};
fhdata = {};
shdata = {};
sold = s0;

Print["Starting simulation with parallel computation..."];
Print["Parameters: hmax=", hmax, ", hmin=", hmin, ", vadv=", vadv, 
  ", vrec=", vrec];
Print["Time range: t0=", t0, ", tend=", tend, ", dt=", dt];

startIter = Round[(tend - t0)/(tstep vadv)];
endIter = Round[(tend - t0)/tstep];
Print["Iteration range: ", startIter, " to ", endIter];

Table[
  t = t0 + tstep i;
  If[undone,
   If[i == startIter, stnow = s0,
    st = sold;
    stnow = getsnext[t - tstep, st];
    If[! NumericQ[stnow], 
     Print["Error detected at iteration ", i, ", t=", t, ", h=", h[t], 
       ": ", stnow];
     undone = False; 
     Break[];];
    If[undone && stnow < hsmin[h[t]], stnow = hsmin[h[t]]];
    If[undone && stnow < 0, stnow = 0; undone = False];
    If[undone,
     energy1 = getenergy[stnow, h[t], w, b, lb, deltal];
     If[NumericQ[energy1[[1]]] && energy1[[1]] != 10000,
      force1 = energy1[[3]];
      ftnow = \[Zeta]h h'[t] + force1;,
      Print["Energy calculation failed at iteration ", i, ", t=", t];
      undone = False;
      Break[];
      ];
     ];
    ];
   sold = stnow;
   If[Mod[i, 500] == 0 && undone, 
    Print["Iteration ", i, ": t=", N[t, 4], ", s=", N[stnow, 4], 
      ", force=", N[ftnow, 4], ", h=", N[h[t], 4]]];
   If[Mod[i, 100] == 0 && undone,
    AppendTo[sdata, {t, stnow}];
    AppendTo[fdata, {t, ftnow}];
    AppendTo[fhdata, {h[t], ftnow}];
    AppendTo[shdata, {h[t], stnow}];
    ]];
  , {i, startIter, endIter}];

Print["Simulation completed. Data points collected: ", Length[sdata]];

If[Length[sdata] > 0,
 sdataalpha0v0dot01 = sdata;
 fdataalpha0v0dot01 = fdata;
 fhdataalpha0v0dot01 = fhdata;
 shdataalpha0v0dot01 = shdata;
 
 storedata[{"sdataalpha0v0dot01", "fdataalpha0v0dot01", 
   "fhdataalpha0v0dot01", "shdataalpha0v0dot01"}];
 Print["Data saved to CSV files."];,
 Print["No data to save - simulation failed."];
 ]

(* Clean up parallel kernels *)
CloseKernels[];
