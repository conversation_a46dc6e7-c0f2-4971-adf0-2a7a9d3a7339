import numpy as np
import pandas as pd
from scipy.integrate import solve_bvp, quad
from scipy.interpolate import interp1d
import os
import gc
from multiprocessing import Pool
from functools import partial
import warnings
warnings.filterwarnings('ignore')

# Directory setup
foldername = "0512"
if not os.path.exists(foldername):
    os.makedirs(foldername)
os.chdir(foldername)

def storedata(data_dict):
    """Store data to CSV files"""
    for var_name, data in data_dict.items():
        df = pd.DataFrame(data)
        df.to_csv(f"{var_name}.csv", index=False, header=False)

def getdata(file_list):
    """Load data from CSV files"""
    data_dict = {}
    for filename in file_list:
        try:
            data = pd.read_csv(f"{filename}.csv", header=None).values
            data_dict[filename] = data
        except FileNotFoundError:
            print(f"Warning: {filename}.csv not found")
            data_dict[filename] = np.array([[0, 0]])  # Default data
    return data_dict

# Load hsmindata and create interpolation
try:
    data_dict = getdata(["hsmindata"])
    hsmindata = data_dict["hsmindata"]
    hsmin = interp1d(hsmindata[:, 0], hsmindata[:, 1], 
                     kind='linear', fill_value='extrapolate')
except:
    # Create dummy interpolation if data not available
    hsmin = lambda h: 0.0001

def getenergy(sc, h_val, w, b, lb, deltal):
    """
    Solve boundary value problem and compute energy
    Returns [energy, lambda10, lambda20] or [10000, 10000, 10000] on failure
    """
    try:
        l = lb - sc
        y0m = 4/np.pi * np.sqrt(lb * deltal)
        ym = y0m - h_val
        
        def ode_system(s, y):
            """ODE system: [theta, psi, x, y_pos, lambda1, lambda2]"""
            theta, psi, x, y_pos, lambda1, lambda2 = y
            
            dtheta_ds = psi
            dpsi_ds = (lambda2 * np.cos(theta) - lambda1 * np.sin(theta)) / b
            dx_ds = np.cos(theta)
            dy_ds = np.sin(theta)
            dlambda1_ds = 0
            dlambda2_ds = 0
            
            return np.array([dtheta_ds, dpsi_ds, dx_ds, dy_ds, dlambda1_ds, dlambda2_ds])
        
        def boundary_conditions(ya, yb):
            """Boundary conditions"""
            theta_0, psi_0, x_0, y_0, lambda1_0, lambda2_0 = ya
            theta_l, psi_l, x_l, y_l, lambda1_l, lambda2_l = yb
            
            return np.array([
                x_0,                    # x(0) = 0
                y_0,                    # y(0) = 0  
                theta_0,                # theta(0) = 0
                x_l - (l - deltal),     # x(l) = l - deltal
                y_l - ym,               # y(l) = ym
                psi_l                   # psi(l) = 0
            ])
        
        # Initial guess
        s_mesh = np.linspace(0, l, 50)
        y_init = np.zeros((6, len(s_mesh)))
        y_init[0] = np.linspace(0, 0.1, len(s_mesh))  # theta
        y_init[2] = np.linspace(0, l - deltal, len(s_mesh))  # x
        y_init[3] = np.linspace(0, ym, len(s_mesh))  # y
        
        # Solve BVP
        sol = solve_bvp(ode_system, boundary_conditions, s_mesh, y_init, tol=1e-6)
        
        if not sol.success:
            return [10000, 10000, 10000]
        
        # Check if psi(0) > 0
        psi_0 = sol.sol(0)[1]
        if psi_0 <= 0:
            return [10000, 10000, 10000]
        
        # Compute energy integral
        def integrand(s):
            return sol.sol(s)[1]**2  # psi(s)^2
        
        energy_integral, _ = quad(integrand, 0, l)
        energy = energy_integral - w * sc
        
        lambda1_0 = sol.sol(0)[4]
        lambda2_0 = sol.sol(0)[5]
        
        # Memory cleanup
        del sol
        gc.collect()
        
        return [energy, lambda1_0, lambda2_0]
        
    except Exception as e:
        return [10000, 10000, 10000]

def getonlyenergy(sc, h_val, w, b, lb, deltal):
    """
    Compute only energy (optimized version)
    Returns energy or 10000 on failure
    """
    result = getenergy(sc, h_val, w, b, lb, deltal)
    return result[0]

def compute_energy_parallel(args):
    """Helper function for parallel computation"""
    ss, h1, w, b, lb, deltal = args
    return getonlyenergy(ss, h1, w, b, lb, deltal)

def getduds(t, s0, h_func, ds, w, b, lb, deltal):
    """
    Compute numerical derivative du/ds using finite differences
    """
    h1 = h_func(t)
    
    # Try central difference first
    points = [s0 + i*ds for i in [-2, -1, 1, 2]]
    
    # Prepare arguments for parallel computation
    args_list = [(ss, h1, w, b, lb, deltal) for ss in points]
    
    # Use multiprocessing for parallel computation
    try:
        with Pool(processes=4) as pool:
            values = pool.map(compute_energy_parallel, args_list)
    except:
        # Fallback to sequential computation
        values = [compute_energy_parallel(args) for args in args_list]
    
    left2, left1, right1, right2 = values
    
    if all(v != 10000 for v in [left2, left1, right1, right2]):
        der = (-right2 + 8*right1 - 8*left1 + left2) / (12*ds)
        return der
    
    # Try forward difference
    points = [s0 + i*ds for i in [0, 1, 2, 3, 4]]
    args_list = [(ss, h1, w, b, lb, deltal) for ss in points]
    
    try:
        with Pool(processes=4) as pool:
            values = pool.map(compute_energy_parallel, args_list)
    except:
        values = [compute_energy_parallel(args) for args in args_list]
    
    center, right1, right2, right3, right4 = values
    
    if all(v != 10000 for v in [center, right1, right2, right3, right4]):
        der = (-25*center + 48*right1 - 36*right2 + 16*right3 - 3*right4) / (12*ds)
        return der
    
    # Try backward difference
    points = [s0 + i*ds for i in [-4, -3, -2, -1, 0]]
    args_list = [(ss, h1, w, b, lb, deltal) for ss in points]
    
    try:
        with Pool(processes=4) as pool:
            values = pool.map(compute_energy_parallel, args_list)
    except:
        values = [compute_energy_parallel(args) for args in args_list]
    
    left4, left3, left2, left1, center = values
    
    if all(v != 10000 for v in [center, left4, left3, left2, left1]):
        der = (3*left4 - 16*left3 + 36*left2 - 48*left1 + 25*center) / (12*ds)
        return der
    
    return "Error_at_getduds"

def sdot(t, s, getduds_func, c, w):
    """Compute ds/dt"""
    duds_result = getduds_func(t, s)
    if isinstance(duds_result, (int, float)):
        return -(1/c) * (duds_result - w)
    else:
        return duds_result

def getsnext(t, s, dt, sdot_func):
    """4th order Runge-Kutta integration"""
    k1 = sdot_func(t, s) * dt
    if not isinstance(k1, (int, float)):
        return k1
    
    k2 = sdot_func(t + dt/2, s + k1/2) * dt
    if not isinstance(k2, (int, float)):
        return k2
    
    k3 = sdot_func(t + dt/2, s + k2/2) * dt
    if not isinstance(k3, (int, float)):
        return k3
    
    k4 = sdot_func(t + dt, s + k3) * dt
    if not isinstance(k4, (int, float)):
        return k4
    
    snext = s + (k1 + 2*k2 + 2*k3 + k4) / 6
    return snext

# Parameters
# Indentation parameters
hmax = 0.1
hmin = 0
vadv = 0.05
vrec = -0.05
t0 = 0
tstep = 0.001
tadv1 = hmax / vadv
trec = (hmin - hmax) / vrec
tadv2 = (hmax - hmin) / vadv
tend = tadv1 + trec
dt = tstep
ds = 0.001

# Material parameters
lb = 1
w = 1
deltal = 1/20
b = 1
zeta_h = 0.01
alpha = 0
c = 0.05

def h(t):
    """Piecewise function for h(t)"""
    if t < tadv1:
        return vadv * t
    elif tadv1 <= t <= trec + tadv1:
        return hmax + vrec * (t - tadv1)
    elif tadv1 + trec < t <= tadv1 + trec + tadv2:
        return hmin + vadv * (t - (tadv1 + trec))
    elif tadv1 + trec + tadv2 <= t <= tadv1 + trec + tadv2 + trec:
        return hmax + vrec * (t - (tadv1 + trec + tadv2))
    elif tadv1 + trec + tadv2 + trec < t <= tadv1 + trec + tadv2 + trec + tadv2:
        return hmin + vadv * (t - (tadv1 + trec + tadv2 + trec))
    else:
        return hmax + vrec * (t - (tadv1 + trec + tadv2 + trec + tadv2))

def h_derivative(t, epsilon=1e-8):
    """Numerical derivative of h(t)"""
    return (h(t + epsilon) - h(t - epsilon)) / (2 * epsilon)

# Set initial state
s0 = 0.001
ftnow = zeta_h * h_derivative(t0)
undone = True

# Pre-allocate lists for better performance
sdata = []
fdata = []
fhdata = []
shdata = []
sold = s0

# Create partial functions for easier passing
getduds_partial = partial(getduds, h_func=h, ds=ds, w=w, b=b, lb=lb, deltal=deltal)
sdot_partial = partial(sdot, getduds_func=getduds_partial, c=c, w=w)
getsnext_partial = partial(getsnext, dt=dt, sdot_func=sdot_partial)

# Main simulation loop
start_i = round((tend - t0) / (tstep * vadv))
end_i = round((tend - t0) / tstep)

print(f"Starting simulation from iteration {start_i} to {end_i}")

for i in range(start_i, end_i + 1):
    t = t0 + tstep * i
    
    if undone:
        if i == start_i:
            stnow = s0
        else:
            st = sold
            stnow = getsnext_partial(t - tstep, st)
            
            if not isinstance(stnow, (int, float)):
                print(f"Error detected at h={h(t)}: {stnow}")
                undone = False
                continue
            
            if stnow < hsmin(h(t)):
                stnow = hsmin(h(t))
            
            if stnow < 0:
                stnow = 0
                undone = False
            
            if undone:
                energy1 = getenergy(stnow, h(t), w, b, lb, deltal)
                force1 = energy1[2]  # lambda2_0
                ftnow = zeta_h * h_derivative(t) + force1
        
        sold = stnow
        
        if i % 500 == 0 and undone:
            print(f"t: {t:.6f}, s: {stnow:.6f}, force: {ftnow:.6f}, h[t]: {h(t):.6f}, hsmin[h[t]]: {hsmin(h(t)):.6f}")
        
        # Store data every 100 iterations
        if i % 100 == 0 and undone:
            sdata.append([t, stnow])
            fdata.append([t, ftnow])
            fhdata.append([h(t), ftnow])
            shdata.append([h(t), stnow])

# Store final results
results = {
    "sdataalpha0v0dot01": sdata,
    "fdataalpha0v0dot01": fdata,
    "fhdataalpha0v0dot01": fhdata,
    "shdataalpha0v0dot01": shdata
}

storedata(results)
print("Simulation completed and data saved.")
