#!/usr/bin/env python3
"""
SUN.EXE 专用分析工具
针对2008年时期嵌入式系统自定义格式的深度分析
"""

import struct
import binascii
import math
from collections import Counter
import re

class SUNExeAnalyzer:
    def __init__(self, filename):
        """初始化分析器"""
        with open(filename, 'rb') as f:
            self.data = f.read()
        self.size = len(self.data)
        self.filename = filename
        
    def analyze_header(self):
        """分析文件头结构"""
        print("=== 文件头分析 ===")
        
        # 基本信息
        magic = self.data[:4]
        name = self.data[4:8]
        version_area = self.data[8:16]
        
        print(f"Magic: {magic.hex()} ({magic})")
        print(f"Name: {name.hex()} ({name.decode('ascii', errors='ignore')})")
        print(f"Version area: {version_area.hex()}")
        
        # 分析可能的头部字段
        print("\n可能的头部字段:")
        for i in range(16, min(64, self.size), 4):
            value_le = struct.unpack('<I', self.data[i:i+4])[0]
            value_be = struct.unpack('>I', self.data[i:i+4])[0]
            
            # 检查是否是合理的偏移或大小
            if 0 < value_le < self.size:
                print(f"  Offset 0x{i:02x}: {value_le:8d} (0x{value_le:08x}) LE - 可能的偏移/大小")
            if 0 < value_be < self.size and value_be != value_le:
                print(f"  Offset 0x{i:02x}: {value_be:8d} (0x{value_be:08x}) BE - 可能的偏移/大小")
    
    def analyze_strings(self):
        """分析字符串资源"""
        print("\n=== 字符串分析 ===")
        
        # 提取ASCII字符串
        ascii_strings = []
        current = b""
        positions = []
        
        for i, byte in enumerate(self.data):
            if 32 <= byte <= 126:  # 可打印ASCII
                if not current:
                    positions.append(i)
                current += bytes([byte])
            else:
                if len(current) > 3:
                    ascii_strings.append((positions[-1], current.decode('ascii')))
                current = b""
        
        # 显示重要字符串
        print("重要字符串:")
        for pos, string in ascii_strings:
            if len(string) > 5:
                print(f"  0x{pos:04x}: {string}")
        
        # 分析语言资源
        languages = [b'default', b'gb2312', b'uigur', b'english', b'japanese', b'french']
        print("\n语言资源位置:")
        for lang in languages:
            pos = self.data.find(lang)
            if pos != -1:
                print(f"  {lang.decode()}: 0x{pos:04x}")
                
                # 分析语言块周围的结构
                context_start = max(0, pos - 16)
                context_end = min(self.size, pos + len(lang) + 16)
                context = self.data[context_start:context_end]
                print(f"    上下文: {context.hex()}")
    
    def analyze_code_sections(self):
        """分析可能的代码段"""
        print("\n=== 代码段分析 ===")
        
        # 查找ARM指令模式
        arm_patterns = []
        for i in range(0, self.size - 4, 4):
            word = struct.unpack('<I', self.data[i:i+4])[0]
            
            # ARM分支指令 (B/BL)
            if 0xEA000000 <= word <= 0xEBFFFFFF:
                arm_patterns.append((i, word, "ARM Branch"))
            
            # ARM加载指令 (LDR)
            elif (word & 0x0E000000) == 0x04000000:
                arm_patterns.append((i, word, "ARM Load"))
        
        if arm_patterns:
            print("可能的ARM指令:")
            for pos, instr, desc in arm_patterns[:10]:  # 只显示前10个
                print(f"  0x{pos:04x}: 0x{instr:08x} - {desc}")
        
        # 查找MIPS指令模式
        mips_patterns = []
        for i in range(0, self.size - 4, 4):
            word = struct.unpack('>I', self.data[i:i+4])[0]  # MIPS通常是大端
            
            # MIPS跳转指令
            if (word & 0xFC000000) == 0x08000000:  # J instruction
                mips_patterns.append((i, word, "MIPS Jump"))
        
        if mips_patterns:
            print("可能的MIPS指令:")
            for pos, instr, desc in mips_patterns[:5]:
                print(f"  0x{pos:04x}: 0x{instr:08x} - {desc}")
    
    def analyze_data_structures(self):
        """分析数据结构"""
        print("\n=== 数据结构分析 ===")
        
        # 查找重复模式
        patterns = {}
        for length in [4, 8, 12, 16]:
            for i in range(0, self.size - length, length):
                pattern = self.data[i:i+length]
                if pattern in patterns:
                    patterns[pattern].append(i)
                else:
                    patterns[pattern] = [i]
        
        print("重复的数据模式:")
        for pattern, positions in patterns.items():
            if len(positions) >= 3 and len(pattern) >= 4:
                print(f"  模式 {pattern.hex()}: 出现在 {positions[:5]}...")
        
        # 查找可能的表结构
        print("\n可能的表结构:")
        for i in range(0, self.size - 32, 4):
            # 检查是否是指针表
            pointers = []
            for j in range(4):
                if i + j*4 + 4 <= self.size:
                    ptr = struct.unpack('<I', self.data[i + j*4:i + j*4 + 4])[0]
                    if 0 < ptr < self.size:
                        pointers.append(ptr)
            
            if len(pointers) >= 3:
                print(f"  0x{i:04x}: 可能的指针表 {[hex(p) for p in pointers]}")
    
    def analyze_entropy(self):
        """分析文件熵（检测加密/压缩）"""
        print("\n=== 熵分析 ===")
        
        # 计算整体熵
        counter = Counter(self.data)
        entropy = -sum((count/self.size) * math.log2(count/self.size) 
                      for count in counter.values())
        
        print(f"整体熵: {entropy:.3f} (0=完全有序, 8=完全随机)")
        
        if entropy < 3:
            print("  → 低熵，可能包含大量重复数据或文本")
        elif entropy > 7:
            print("  → 高熵，可能经过加密或压缩")
        else:
            print("  → 中等熵，典型的二进制程序")
        
        # 分段熵分析
        chunk_size = 1024
        print(f"\n分段熵分析 (每{chunk_size}字节):")
        for i in range(0, self.size, chunk_size):
            chunk = self.data[i:i+chunk_size]
            if len(chunk) < 100:  # 跳过太小的块
                continue
                
            chunk_counter = Counter(chunk)
            chunk_entropy = -sum((count/len(chunk)) * math.log2(count/len(chunk)) 
                               for count in chunk_counter.values())
            
            if chunk_entropy > 7 or chunk_entropy < 2:
                print(f"  0x{i:04x}-0x{i+len(chunk):04x}: {chunk_entropy:.3f}")
    
    def find_xd868_signatures(self):
        """查找XD868平台特征"""
        print("\n=== XD868平台特征分析 ===")
        
        # 查找平台标识
        platform_strings = [b'XD868', b'xd868', b'Spreadtrum', b'SPRD']
        for string in platform_strings:
            pos = self.data.find(string)
            if pos != -1:
                print(f"  平台标识 '{string.decode()}' 在 0x{pos:04x}")
        
        # 查找可能的寄存器地址（XD868特有）
        # 这些是假设的地址，实际需要查阅XD868技术手册
        register_addresses = [
            0x40000000,  # 可能的外设基地址
            0x80000000,  # 可能的内存基地址
            0x20000000,  # 可能的SRAM地址
        ]
        
        for addr in register_addresses:
            addr_bytes = struct.pack('<I', addr)
            pos = self.data.find(addr_bytes)
            if pos != -1:
                print(f"  可能的寄存器地址 0x{addr:08x} 在 0x{pos:04x}")
    
    def generate_report(self):
        """生成完整分析报告"""
        print(f"{'='*60}")
        print(f"SUN.EXE 深度分析报告")
        print(f"文件: {self.filename}")
        print(f"大小: {self.size} 字节")
        print(f"{'='*60}")
        
        self.analyze_header()
        self.analyze_strings()
        self.analyze_code_sections()
        self.analyze_data_structures()
        self.analyze_entropy()
        self.find_xd868_signatures()
        
        print(f"\n{'='*60}")
        print("分析完成")
        print(f"{'='*60}")

def main():
    """主函数"""
    try:
        analyzer = SUNExeAnalyzer('SUN.EXE')
        analyzer.generate_report()
    except FileNotFoundError:
        print("错误: 找不到 SUN.EXE 文件")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    main()
