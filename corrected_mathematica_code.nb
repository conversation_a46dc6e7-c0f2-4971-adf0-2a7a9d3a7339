foldername = "0512";
SetDirectory[NotebookDirectory[]]; 
CreateDirectory[foldername]; 
SetDirectory[foldername]; 

storedata[dataList_] := 
  Module[{varName}, Table[varName = ToString[dataList[[i]]];
    Export[varName <> ".csv", ToExpression[dataList[[i]]]];, {i, 
     Length[dataList]}]];

getdata[fileList_] := 
  Module[{}, 
   Table[ToExpression[
     fileList[[i]] <> "= Import[\"" <> fileList[[i]] <> 
      ".csv\"]"], {i, Length[fileList]}]];

getdata[{"hsmindata"}];
hsmin = Interpolation[hsmindata];(*h, Subscript[s, min] *)

getenergy[sc_?NumericQ, h_?NumericQ, w_?NumericQ, b_?NumericQ, 
   lb_?NumericQ, deltal_?NumericQ] := 
  Module[{ym, \[Theta], \[Psi], l, \[Lambda]1, \[Lambda]2, x, y, ode, 
    sol, bc, s, y0m, energy, \[Lambda]10, \[Lambda]20},
   l = lb - sc;
   y0m = 4/\[Pi] (lb deltal)^(1/2);
   ym = y0m - h;
   ode = {Derivative[1][\[Theta]][s] == \[Psi][s],
     b \[Psi]'[
        s] == \[Lambda]2[s] Cos[\[Theta][s]] - \[Lambda]1[
         s] Sin[\[Theta][s]],
     x'[s] == Cos[\[Theta][s]],
     y'[s] == Sin[\[Theta][s]],
     \[Lambda]1'[s] == 0,
     \[Lambda]2'[s] == 0};
   bc = {x[0] == 0,
     y[0] == 0,
     \[Theta][0] == 0,
     x[l] == l - deltal,
     y[l] == ym,
     \[Psi][l] == 0};
   sol = 
    Quiet@Check[
      NDSolve[{ode, bc}, {\[Theta], \[Psi], x, 
        y, \[Lambda]1, \[Lambda]2}, {s, 0, l}], ($Failed)];
   If[sol =!= $Failed ,
    If[(\[Psi][0] /. sol[[1]]) > 0,
     energy = NIntegrate[(\[Psi][s] /. sol[[1]])^2, {s, 0, l}] - w sc;
     \[Lambda]10 = \[Lambda]1[0] /. sol[[1]];
     \[Lambda]20 = \[Lambda]2[0] /. sol[[1]];
     Clear[sol];  (* Only clear sol, not the symbols *)
     {energy, \[Lambda]10, \[Lambda]20}, 
     Clear[sol]; {10000, 10000, 10000}],
    Clear[sol]; {10000, 10000, 10000}]];

getonlyenergy[sc_?NumericQ, h_?NumericQ, w_?NumericQ, b_?NumericQ, 
   lb_?NumericQ, deltal_?NumericQ] := 
  Module[{ym, \[Theta], \[Psi], l, \[Lambda]1, \[Lambda]2, x, y, ode, 
    sol, bc, s, y0m, energy},
   l = lb - sc;
   y0m = 4/\[Pi] (lb deltal)^(1/2);
   ym = y0m - h;
   ode = {Derivative[1][\[Theta]][s] == \[Psi][s],
     b \[Psi]'[
        s] == \[Lambda]2[s] Cos[\[Theta][s]] - \[Lambda]1[
         s] Sin[\[Theta][s]],
     x'[s] == Cos[\[Theta][s]],
     y'[s] == Sin[\[Theta][s]],
     \[Lambda]1'[s] == 0,
     \[Lambda]2'[s] == 0};
   bc = {x[0] == 0,
     y[0] == 0,
     \[Theta][0] == 0,
     x[l] == l - deltal,
     y[l] == ym,
     \[Psi][l] == 0};
   sol = 
    Quiet@Check[
      NDSolve[{ode, bc}, {\[Theta], \[Psi], x, 
        y, \[Lambda]1, \[Lambda]2}, {s, 0, l}], ($Failed)];
   If[sol =!= $Failed ,
    If[(\[Psi][0] /. sol[[1]]) > 0,
     energy = NIntegrate[(\[Psi][s] /. sol[[1]])^2, {s, 0, l}] - w sc;
     Clear[sol]; (* Only clear sol, not the symbols *)
     energy, 
     Clear[sol]; 10000],
    Clear[sol]; 10000]];

getduds[t_, s0_] := 
 Module[{points, values, left4, left3, left2, left1, center, right1, 
   right2, right3, right4, der, h1},
  h1 = h[t];
  points = Table[s0 + i*ds, {i, {-2, -1, 1, 2}}];
  values = 
   ParallelTable[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points},
     Method -> "CoarsestGrained"];
  {left2, left1, right1, right2} = values;
  If[left2 != 10000 && left1 != 10000 && right1 != 10000 && 
    right2 != 10000, 
   der = (-right2 + 8*right1 - 8*left1 + left2)/(12*ds);
   Return[der];];
  points = Table[s0 + i*ds, {i, {0, 3, 4}}];
  values = 
   ParallelTable[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points},
     Method -> "CoarsestGrained"];
  {center, right3, right4} = values;
  If[center != 10000 && right1 != 10000 && right2 != 10000 && 
    right3 != 10000 && right4 != 10000, 
   der = (-25*center + 48*right1 - 36*right2 + 16*right3 - 
       3*right4)/(12*ds); Return[der];];
  points = Table[s0 + i*ds, {i, {-4, -3}}];
  values = 
   ParallelTable[getonlyenergy[ss, h1, w, b, lb, deltal], {ss, points},
     Method -> "CoarsestGrained"];
  {left4, left3} = values;
  If[center != 10000 && left4 != 10000 && left3 != 10000 && 
    left2 != 10000 && left1 != 10000, 
   der = (3*left4 - 16*left3 + 36*left2 - 48*left1 + 25*center)/(12*
       ds);
   Return[der];, Return["Error_at_getduds"];]]

getsnext[t_, s_] := 
  Module[{k1, k2, k3, k4, snext},
   k1 = sdot[t, s] dt;
   If[! NumericQ[k1], Return[k1]];
   k2 = sdot[t + dt/2, s + k1/2] dt;
   If[! NumericQ[k2], Return[k2]];
   k3 = sdot[t + dt/2, s + k2/2] dt;
   If[! NumericQ[k3], Return[k3]];
   k4 = sdot[t + dt, s + k3] dt;
   If[! NumericQ[k4], Return[k4]];
   snext = s + 1/6 (k1 + 2 k2 + 2 k3 + k4)];

(* Fixed redundant function call *)
sdot[t_, s_] := Module[{dudsResult}, 
  dudsResult = getduds[t, s];
  If[NumericQ[dudsResult], -(1/c) (dudsResult - w), dudsResult]]

(*Indentation parameters;*)
hmax = 0.1;
hmin = 0;
vadv = 0.05;
vrec = -0.05;
t0 = 0;
tstep = 0.001;
tadv1 = hmax/vadv;
trec = (hmin - hmax)/vrec;
tadv2 = (hmax - hmin)/vadv;
tend = tadv1 + trec;
dt = tstep;
ds = 0.001;

(*material parameters;*)
lb = 1;
w = 1;
deltal = 1/20; 
b = 1;
\[Zeta]h = 0.01;
\[Alpha] = 0;
c = 0.05;

h[t_] := 
  Piecewise[{{vadv t, t < tadv1}, {hmax + vrec (t - tadv1), 
     trec + tadv1 >= t >= tadv1}, {hmin + vadv (t - (tadv1 + trec)), 
     tadv1 + trec + tadv2 > t > tadv1 + trec}, {hmax + 
      vrec (t - (tadv1 + trec + tadv2)), 
     tadv1 + trec + tadv2 + trec >= t >= 
      tadv1 + trec + tadv2}, {hmin + 
      vadv (t - (tadv1 + trec + tadv2 + trec)), 
     tadv1 + trec + tadv2 + trec + tadv2 > t > 
      tadv1 + trec + tadv2 + trec}, {hmax + 
      vrec (t - (tadv1 + trec + tadv2 + trec + tadv2)), 
     t >= tadv1 + trec + tadv2 + trec + tadv2}}];

(*Set_initial_state*)
s0 = 0.001;
ftnow = \[Zeta]h h'[t0];
undone = True;

sdata = {};
fdata = {};
fhdata = {};
shdata = {};
sold = s0;

Table[
  t = t0 + tstep i;
  If[undone,
   If[i == 0, stnow = s0,
    st = sold;
    stnow = getsnext[t - tstep, st];
    If[! NumericQ[stnow], 
     Print["Error detected at h=", h[t], ": ", stnow];
     undone = False; ];
    If[stnow < hsmin[h[t]], stnow = hsmin[h[t]]];
    If[stnow < 0, stnow = 0; undone = False];
    If[undone,
     energy1 = getenergy[stnow, h[t], w, b, lb, deltal];
     force1 = energy1[[3]];
     ftnow = \[Zeta]h h'[t] + force1;];
    Print["t:", t, " s:", stnow, ",  force:", ftnow, "   h[t]: ", h[t],
      "  hsmin[h[t]]: ", hsmin[h[t]]];
    ];
   sold = stnow;
   If[Mod[i, 500] == 0 && undone, 
    Print["t:", t, " s:", stnow, ",  force:", ftnow, "   h[t]: ", h[t],
      "  hsmin[h[t]]: ", hsmin[h[t]]]];
   (* CRITICAL FIX: Use AppendTo instead of Join to avoid exponential slowdown *)
   If[Mod[i, 100] == 0 && undone,
    AppendTo[sdata, {t, stnow}];
    AppendTo[fdata, {t, ftnow}];
    AppendTo[fhdata, {h[t], ftnow}];
    AppendTo[shdata, {h[t], stnow}];
    ]];
  , {i, Round[(tend - t0)/(tstep  vadv)], Round[(tend - t0)/tstep]}];

sdataalpha0v0dot01 = sdata;
fdataalpha0v0dot01 = fdata;
fhdataalpha0v0dot01 = fhdata;
shdataalpha0v0dot01 = shdata;

storedata[{"sdataalpha0v0dot01", "fdataalpha0v0dot01", 
  "fhdataalpha0v0dot01", "shdataalpha0v0dot01"}]
