# Mathematica to Python Translation

This repository contains the Python translation of the optimized Mathematica code for solving a boundary value problem with numerical integration.

## Files

- `optimized_python_code.py` - Main Python implementation
- `optimized_mathematica_code.nb` - Original optimized Mathematica code
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## Installation

1. Install required Python packages:
```bash
pip install -r requirements.txt
```

2. Ensure you have the `hsmindata.csv` file in the same directory (if available)

## Key Translation Notes

### Major Changes from Mathematica to Python:

1. **NDSolve → scipy.integrate.solve_bvp**
   - Mathematica's `NDSolve` is replaced with SciPy's boundary value problem solver
   - Boundary conditions are handled differently but maintain the same mathematical meaning

2. **NIntegrate → scipy.integrate.quad**
   - Numerical integration using SciPy's quadrature methods

3. **ParallelTable → multiprocessing.Pool**
   - Parallel computation using Python's multiprocessing module
   - Falls back to sequential computation if parallel fails

4. **Interpolation → scipy.interpolate.interp1d**
   - Linear interpolation with extrapolation for `hsmin` function

5. **Memory Management**
   - Python's garbage collection replaces Mathematica's `Clear` and `Remove`
   - Explicit `gc.collect()` calls for better memory management

### Performance Optimizations Maintained:

1. **List Growth**: Uses Python lists with `append()` instead of Mathematica's `Join[]`
2. **Function Call Optimization**: Eliminates redundant function calls
3. **Memory Cleanup**: Proper cleanup of large objects

### Key Functions:

- `getenergy()`: Solves the boundary value problem and returns energy and lambda values
- `getonlyenergy()`: Optimized version that returns only energy
- `getduds()`: Computes numerical derivatives using finite differences
- `sdot()`: Computes ds/dt for the ODE system
- `getsnext()`: 4th-order Runge-Kutta integration
- `h()`: Piecewise function for the height profile

## Usage

Simply run the Python script:

```bash
python optimized_python_code.py
```

The script will:
1. Create a "0512" directory
2. Load `hsmindata.csv` if available (creates dummy data if not)
3. Run the main simulation loop
4. Save results to CSV files:
   - `sdataalpha0v0dot01.csv`
   - `fdataalpha0v0dot01.csv`
   - `fhdataalpha0v0dot01.csv`
   - `shdataalpha0v0dot01.csv`

## Parameters

The simulation uses the same parameters as the original Mathematica code:

- **Indentation parameters**: `hmax=0.1`, `hmin=0`, `vadv=0.05`, `vrec=-0.05`
- **Material parameters**: `lb=1`, `w=1`, `deltal=1/20`, `b=1`
- **Numerical parameters**: `dt=0.001`, `ds=0.001`, `c=0.05`, `zeta_h=0.01`

## Expected Output

The script prints progress information during execution and saves the final results to CSV files. The simulation should run significantly faster than the original unoptimized Mathematica version due to the performance fixes.

## Troubleshooting

1. **Missing hsmindata.csv**: The script will create dummy interpolation data if the file is missing
2. **Parallel processing issues**: The code falls back to sequential computation if multiprocessing fails
3. **Memory issues**: The script includes garbage collection calls to manage memory usage

## Mathematical Accuracy

The Python translation maintains the same mathematical formulation as the original Mathematica code:
- Same boundary value problem setup
- Same numerical integration methods
- Same finite difference schemes for derivatives
- Same Runge-Kutta integration for time stepping
